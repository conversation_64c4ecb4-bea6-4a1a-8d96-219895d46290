// 文件管理路由
const express = require('express')
const router = express.Router()
const fileController = require('../controllers/fileController')

// 文件列表和查询
router.get('/', fileController.getFileList)
router.get('/stats', fileController.getFileStats)
router.get('/filter-options', fileController.getFilterOptions)
router.get('/:id', fileController.getFileDetail)
router.get('/:id/download', fileController.getDownloadUrl)
router.get('/:id/diagnose', fileController.diagnoseFile)

// 云存储配置检查
router.get('/storage/config', fileController.checkStorageConfig)

// 修复测试数据
router.post('/fix-test-data', fileController.fixTestData)

// 清理孤儿文件（管理员功能）
router.post('/cleanup-orphan-files', fileController.cleanupOrphanFiles)

// 文件上传
router.post('/upload', fileController.uploadSingle, fileController.uploadFile)
router.post('/batch-upload', fileController.uploadMultiple, fileController.batchUploadFiles)

// CSV相关
router.get('/csv/template', fileController.downloadCsvTemplate)
router.post('/csv/upload', fileController.uploadCsv, fileController.csvBatchUpload)

// 文件管理
router.put('/:id', fileController.updateFile)
router.delete('/:id', fileController.deleteFile)
router.post('/batch-delete', fileController.batchDeleteFiles)
router.post('/batch-status', fileController.batchUpdateStatus)

module.exports = router
