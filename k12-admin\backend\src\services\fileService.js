// 文件管理服务
const { app: cloudbase, db } = require('../config/cloudbase')
const fs = require('fs')
const path = require('path')
const sharp = require('sharp')
const csvParser = require('csv-parser')
const csvWriter = require('csv-writer')
const mammoth = require('mammoth')

class FileService {
  constructor() {
    this.db = db
    this.app = cloudbase
    this.filesCollection = this.db.collection('files')
  }

  // 获取文件列表
  async getFileList(params = {}) {
    try {
      const {
        page = 1,
        pageSize = 20,
        grade,
        subject,
        volume,
        section,
        category,
        status = 'active',
        keyword,
        timeRange,    // 新增：时间范围类型（前端传递，后端接收但不直接使用）
        startDate,    // 新增：开始日期
        endDate,      // 新增：结束日期
        sortBy = 'created_time',
        sortOrder = 'desc'
      } = params

      // 构建查询条件
      const where = {}

      // 只有当status不为空字符串时才添加status条件
      if (status && status !== '') {
        where.status = status
      }

      if (grade) where.grade = grade
      if (subject) where.subject = subject
      if (volume) where.volume = volume
      if (section) where.section = section
      if (category) where.category = category

      // 新增：时间范围查询
      if (startDate || endDate) {
        where.created_time = {}
        if (startDate) {
          where.created_time.$gte = new Date(startDate + 'T00:00:00.000Z')
        }
        if (endDate) {
          where.created_time.$lte = new Date(endDate + 'T23:59:59.999Z')
        }
      }

      // 关键词搜索
      if (keyword) {
        where.$or = [
          { title: { $regex: keyword, $options: 'i' } },
          { description: { $regex: keyword, $options: 'i' } },
          { tags: { $in: [keyword] } }
        ]
      }

      // 获取总数
      const countResult = await this.filesCollection.where(where).count()
      const total = countResult.total

      // 分页查询
      const skip = (page - 1) * pageSize
      const orderBy = sortOrder === 'desc' ? 'desc' : 'asc'

      const result = await this.filesCollection
        .where(where)
        .orderBy(sortBy, orderBy)
        .skip(skip)
        .limit(pageSize)
        .get()

      // 转换预览图URL为临时URL
      const filesWithTempUrls = await this.convertPreviewUrls(result.data)

      return {
        success: true,
        data: {
          files: filesWithTempUrls, // 改为files以保持一致性
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(total / pageSize)
        }
      }
    } catch (error) {
      console.error('获取文件列表失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 转换预览图URL为临时URL
  async convertPreviewUrls(files) {
    try {
      const filesWithUrls = []

      for (const file of files) {
        const fileWithUrls = { ...file }

        // 如果有预览图，转换为临时URL
        if (file.preview_images && file.preview_images.length > 0) {
          console.log(`转换文件 ${file._id} 的预览图URL:`, file.preview_images)

          // 处理不同的数据格式
          let fileIds = []
          if (Array.isArray(file.preview_images)) {
            fileIds = file.preview_images.map(item => {
              // 如果是对象格式 {url: "xxx", order: 1}，提取url
              if (typeof item === 'object' && item !== null && item.url) {
                return item.url
              }
              // 如果是字符串格式，直接使用
              if (typeof item === 'string') {
                return item
              }
              // 其他格式返回null，后面会被过滤掉
              return null
            }).filter(id => {
              // 只保留有效的云存储URL
              return typeof id === 'string' && id.trim().startsWith('cloud://')
            })
          }

          // 按order排序（如果原始数据有order字段）
          if (file.preview_images.length > 0 && typeof file.preview_images[0] === 'object' && file.preview_images[0].order !== undefined) {
            const sortedItems = [...file.preview_images].sort((a, b) => (a.order || 0) - (b.order || 0))
            fileIds = sortedItems.map(item => item.url).filter(url => typeof url === 'string' && url.startsWith('cloud://'))
          }

          console.log(`提取的文件ID:`, fileIds)
          console.log(`文件ID类型检查:`, fileIds.map(id => ({ id, type: typeof id })))

          if (fileIds.length === 0) {
            console.log(`文件 ${file._id} 没有有效的预览图ID`)
            fileWithUrls.preview_images = []
            filesWithUrls.push(fileWithUrls)
            continue
          }

          const fileList = fileIds.map(fileId => ({
            fileID: fileId,
            maxAge: 3600 // 1小时有效期
          }))

          console.log(`构建的fileList:`, fileList)

          try {
            const urlResult = await this.app.getTempFileURL({ fileList })
            console.log(`文件 ${file._id} 的预览图转换结果:`, urlResult)

            if (urlResult.fileList && urlResult.fileList.length > 0) {
              const successUrls = []

              urlResult.fileList.forEach(item => {
                if (item.code === 'SUCCESS') {
                  successUrls.push(item.tempFileURL)
                } else if (item.code === 'STORAGE_FILE_NONEXIST') {
                  // 文件不存在是常见情况，不需要警告
                  console.log(`预览图文件不存在: ${item.fileID}`)
                } else {
                  // 其他错误才需要警告
                  console.warn(`预览图转换失败:`, item)
                }
              })

              fileWithUrls.preview_images = successUrls

              if (successUrls.length > 0) {
                console.log(`文件 ${file._id} 成功转换 ${successUrls.length}/${urlResult.fileList.length} 张预览图`)
              } else {
                console.log(`文件 ${file._id} 没有可用的预览图`)
              }
            } else {
              console.warn(`文件 ${file._id} 未返回有效的预览图URL`)
              fileWithUrls.preview_images = []
            }
          } catch (error) {
            console.error(`转换文件 ${file._id} 预览图URL失败:`, error)
            // 如果转换失败，保留原始数据
            fileWithUrls.preview_images = []
          }
        }

        filesWithUrls.push(fileWithUrls)
      }

      return filesWithUrls
    } catch (error) {
      console.error('批量转换预览图URL失败:', error)
      return files // 如果转换失败，返回原始数据
    }
  }

  // 获取文件详情
  async getFileDetail(fileId) {
    try {
      const result = await this.filesCollection.doc(fileId).get()

      if (!result.data.length) {
        return {
          success: false,
          error: '文件不存在'
        }
      }

      // 转换预览图URL
      const filesWithUrls = await this.convertPreviewUrls([result.data[0]])

      return {
        success: true,
        data: filesWithUrls[0]
      }
    } catch (error) {
      console.error('获取文件详情失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 更新文件信息
  async updateFile(fileId, updateData) {
    try {
      // 验证必填字段
      const requiredFields = ['title', 'subject', 'volume', 'section']
      for (const field of requiredFields) {
        if (updateData[field] === undefined || updateData[field] === '') {
          return {
            success: false,
            error: `${field} 字段不能为空`
          }
        }
      }

      // 过滤允许更新的字段
      const allowedFields = [
        'title', 'description', 'grade', 'subject', 'volume', 'section',
        'category', 'tags', 'ad_required_count', 'sort_order', 'features', 'status',
        'download_count', 'view_count' // 添加统计字段支持
      ]

      const filteredData = {}
      for (const field of allowedFields) {
        if (updateData[field] !== undefined) {
          filteredData[field] = updateData[field]
        }
      }

      console.log(`更新文件 ${fileId}:`, filteredData)
      const result = await this.filesCollection.doc(fileId).update(filteredData)
      console.log(`更新结果:`, result)

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('更新文件失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 删除文件（带引用计数检查）
  async deleteFile(fileId) {
    try {
      // 获取文件信息
      const fileResult = await this.getFileDetail(fileId)
      if (!fileResult.success) {
        return fileResult
      }

      const fileData = fileResult.data
      let deletedFiles = []
      let skippedFiles = []

      // 检查并删除云存储中的主文件
      if (fileData.file_url) {
        try {
          // 检查文件URL引用计数
          const fileRefResult = await this.filesCollection
            .where({ file_url: fileData.file_url })
            .count()

          console.log(`文件引用计数检查: ${fileData.file_url} 被 ${fileRefResult.total} 个记录引用`)

          // 只有最后一个引用时才删除云存储文件
          if (fileRefResult.total <= 1) {
            const cloudFileId = fileData.file_url
            const deleteResult = await this.app.deleteFile({
              fileList: [cloudFileId]
            })
            console.log('✅ 主文件删除结果:', deleteResult)
            console.log('✅ 云存储主文件已删除:', cloudFileId)
            deletedFiles.push({ type: 'main_file', url: cloudFileId })
          } else {
            console.log('📎 主文件被其他记录引用，跳过删除:', fileData.file_url)
            skippedFiles.push({ type: 'main_file', url: fileData.file_url, refCount: fileRefResult.total })
          }
        } catch (storageError) {
          console.error('删除云存储主文件失败:', storageError)
          // 主文件删除失败不影响继续处理预览图
        }
      }

      // 检查并删除预览图（使用简化的查询方式）
      if (fileData.preview_images && fileData.preview_images.length > 0) {
        console.log(`开始处理 ${fileData.preview_images.length} 个预览图`)

        // 获取所有文件记录，用于检查预览图引用
        const allFilesResult = await this.filesCollection.get()

        for (const previewImg of fileData.preview_images) {
          try {
            console.log(`检查预览图: ${previewImg.url}`)

            // 统计有多少个文件记录引用了这个预览图
            let refCount = 0
            allFilesResult.data.forEach(file => {
              if (file.preview_images && file.preview_images.some(img => img.url === previewImg.url)) {
                refCount++
                console.log(`  - 被文件 ${file.title} (${file._id}) 引用`)
              }
            })

            console.log(`预览图引用计数: ${previewImg.url} 被 ${refCount} 个记录引用`)

            // 只有最后一个引用时才删除预览图
            if (refCount <= 1) {
              const deleteResult = await this.app.deleteFile({
                fileList: [previewImg.url]
              })
              console.log('✅ 预览图删除结果:', deleteResult)
              console.log('✅ 预览图已删除:', previewImg.url)
              deletedFiles.push({ type: 'preview', url: previewImg.url })
            } else {
              console.log('📎 预览图被其他记录引用，跳过删除:', previewImg.url)
              skippedFiles.push({ type: 'preview', url: previewImg.url, refCount: refCount })
            }
          } catch (previewError) {
            console.error('删除预览图失败:', previewError)
            console.error('预览图URL:', previewImg.url)
            console.error('错误详情:', previewError.stack)
            // 单个预览图删除失败不影响其他预览图的处理
          }
        }
      }

      // 删除数据库记录
      const result = await this.filesCollection.doc(fileId).remove()

      return {
        success: true,
        data: result,
        deletedFiles,
        skippedFiles,
        message: `文件删除成功。已删除 ${deletedFiles.length} 个云存储文件，跳过 ${skippedFiles.length} 个被引用的文件。`
      }
    } catch (error) {
      console.error('删除文件失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量删除文件
  async batchDeleteFiles(fileIds) {
    try {
      const results = []

      for (const fileId of fileIds) {
        const result = await this.deleteFile(fileId)
        results.push({
          fileId,
          success: result.success,
          error: result.error
        })
      }

      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      return {
        success: true,
        data: {
          total: fileIds.length,
          success: successCount,
          failed: failCount,
          results
        }
      }
    } catch (error) {
      console.error('批量删除文件失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量更新文件状态
  async batchUpdateStatus(fileIds, status) {
    try {
      const results = []

      for (const fileId of fileIds) {
        try {
          await this.filesCollection.doc(fileId).update({ status })
          results.push({ fileId, success: true })
        } catch (error) {
          results.push({ fileId, success: false, error: error.message })
        }
      }

      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      return {
        success: true,
        data: {
          total: fileIds.length,
          success: successCount,
          failed: failCount,
          results
        }
      }
    } catch (error) {
      console.error('批量更新状态失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 清理孤儿文件（管理员功能）
  async cleanupOrphanFiles() {
    try {
      console.log('开始清理孤儿文件...')

      // 获取所有文件记录中的URL
      const allFiles = await this.filesCollection.get()
      const usedUrls = new Set()

      // 收集所有被使用的URL
      allFiles.data.forEach(file => {
        if (file.file_url) {
          usedUrls.add(file.file_url)
        }
        if (file.preview_images && file.preview_images.length > 0) {
          file.preview_images.forEach(img => {
            usedUrls.add(img.url)
          })
        }
      })

      console.log(`数据库中共有 ${usedUrls.size} 个被引用的文件URL`)

      // TODO: 这里需要云存储API支持列出所有文件
      // 目前云开发不支持直接列出所有文件，需要其他方式实现

      return {
        success: true,
        message: '孤儿文件清理功能需要云存储API支持，暂未实现',
        usedUrlsCount: usedUrls.size
      }
    } catch (error) {
      console.error('清理孤儿文件失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取文件统计信息
  async getFileStats() {
    try {
      // 总文件数
      const totalResult = await this.filesCollection.where({ status: 'active' }).count()
      const total = totalResult.total

      // 按分类统计
      const categoryStats = await this.filesCollection
        .aggregate()
        .match({ status: 'active' })
        .group({
          _id: '$category',
          count: { $sum: 1 }
        })
        .end()

      // 按年级统计
      const gradeStats = await this.filesCollection
        .aggregate()
        .match({ status: 'active' })
        .group({
          _id: '$grade',
          count: { $sum: 1 }
        })
        .end()

      // 按科目统计
      const subjectStats = await this.filesCollection
        .aggregate()
        .match({ status: 'active' })
        .group({
          _id: '$subject',
          count: { $sum: 1 }
        })
        .end()

      // 按文件类型统计
      const fileTypeStats = await this.filesCollection
        .aggregate()
        .match({ status: 'active' })
        .group({
          _id: '$file_type',
          count: { $sum: 1 }
        })
        .end()

      // 下载量排行榜（前10）
      const downloadRanking = await this.filesCollection
        .where({ status: 'active' })
        .orderBy('download_count', 'desc')
        .limit(10)
        .field({
          title: true,
          download_count: true,
          view_count: true,
          grade: true,
          subject: true
        })
        .get()

      // 今日统计（中国标准时间）
      const now = new Date()
      const chinaOffset = 8 * 60 * 60 * 1000 // 东八区偏移量
      const chinaTime = new Date(now.getTime() + chinaOffset)
      const today = new Date(chinaTime.getFullYear(), chinaTime.getMonth(), chinaTime.getDate())

      const todayUploadsResult = await this.filesCollection.where({
        status: 'active',
        created_time: { $gte: today }
      }).count()

      // 获取最近活动（最近10个文件的创建记录）
      const recentActivity = await this.filesCollection
        .where({ status: 'active' })
        .orderBy('created_time', 'desc')
        .limit(10)
        .field({
          title: true,
          created_time: true,
          grade: true,
          subject: true
        })
        .get()

      // 计算真实存储空间（基于file_size字段）
      const storageResult = await this.filesCollection
        .aggregate()
        .match({ status: 'active' })
        .group({
          _id: null,
          totalSize: { $sum: '$file_size' }
        })
        .end()

      // 将字节转换为MB
      let totalStorageMB = 0
      if (storageResult.list && storageResult.list.length > 0) {
        const totalBytes = storageResult.list[0].totalSize || 0
        totalStorageMB = Math.round((totalBytes / (1024 * 1024)) * 100) / 100 // 转换为MB并保留两位小数
      } else if (storageResult.data && storageResult.data.length > 0) {
        const totalBytes = storageResult.data[0].totalSize || 0
        totalStorageMB = Math.round((totalBytes / (1024 * 1024)) * 100) / 100
      }

      return {
        success: true,
        data: {
          total,
          categoryStats: categoryStats.list || categoryStats.data || [],
          gradeStats: gradeStats.list || gradeStats.data || [],
          subjectStats: subjectStats.list || subjectStats.data || [],
          fileTypeStats: fileTypeStats.list || fileTypeStats.data || [],
          downloadRanking: downloadRanking.data,
          todayUploads: todayUploadsResult.total,
          recentActivity: recentActivity.data,
          totalStorage: totalStorageMB // 真实存储空间（MB）
        }
      }
    } catch (error) {
      console.error('获取文件统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取筛选选项
  async getFilterOptions() {
    try {
      // 获取所有活跃文件的筛选字段
      const result = await this.filesCollection
        .where({ status: 'active' })
        .field({
          grade: true,
          subject: true,
          volume: true,
          section: true,
          category: true
        })
        .get()

      const files = result.data

      // 提取唯一值
      const grades = [...new Set(files.map(f => f.grade).filter(Boolean))]
      const subjects = [...new Set(files.map(f => f.subject).filter(Boolean))]
      const volumes = [...new Set(files.map(f => f.volume).filter(Boolean))]
      const sections = [...new Set(files.map(f => f.section).filter(Boolean))]
      const categories = [...new Set(files.map(f => f.category).filter(Boolean))]

      return {
        success: true,
        data: {
          grades: grades.sort(),
          subjects: subjects.sort(),
          volumes: volumes.sort(),
          sections: sections.sort(),
          categories: categories.sort()
        }
      }
    } catch (error) {
      console.error('获取筛选选项失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }
}

module.exports = new FileService()
