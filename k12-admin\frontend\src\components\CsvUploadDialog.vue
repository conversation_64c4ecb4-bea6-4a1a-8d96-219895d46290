<template>
  <el-dialog
    v-model="visible"
    title="CSV批量上传"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="csv-upload-content">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" align-center class="steps">
        <el-step title="上传CSV文件" />
        <el-step title="数据验证" />
        <el-step title="批量处理" />
        <el-step title="完成" />
      </el-steps>

      <!-- 步骤1: 上传CSV文件 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="step-header">
          <h3>上传CSV文件</h3>
          <p>请上传包含文件信息的CSV文件，确保文件路径正确且文件存在</p>
        </div>

        <div class="upload-section">
          <el-upload
            ref="csvUploadRef"
            :file-list="csvFileList"
            :auto-upload="false"
            accept=".csv"
            :before-upload="beforeCsvUpload"
            :on-change="handleCsvFileChange"
            :on-remove="handleCsvFileRemove"
            drag
            class="csv-upload-area"
          >
            <div class="upload-content">
              <el-icon class="upload-icon"><DocumentAdd /></el-icon>
              <div class="upload-text">
                <p>将CSV文件拖到此处，或<em>点击上传</em></p>
                <p class="upload-tip">
                  仅支持 .csv 格式文件，建议使用UTF-8编码
                </p>
              </div>
            </div>
          </el-upload>

          <div class="template-section">
            <el-alert
              title="使用说明"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <p>1. 请先下载CSV模板文件，按照模板格式填写文件信息（支持15个字段）</p>
                <p>2. 确保文件路径列中的文件真实存在且可访问</p>
                <p>3. 必填字段：文件路径、标题、科目、册别、板块</p>
                <p>4. 分类字段在年级字段之前，注意字段顺序</p>
                <p>5. 文件特征和标签必须用分号(;)分隔多个值</p>
              </template>
            </el-alert>

            <div class="template-actions">
              <el-button type="primary" @click="downloadTemplate">
                <el-icon><Download /></el-icon>
                下载CSV模板
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 数据验证 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-header">
          <h3>数据验证</h3>
          <p>正在验证CSV文件中的数据...</p>
        </div>

        <div v-if="validating" class="validation-loading">
          <el-icon class="is-loading" size="24"><Loading /></el-icon>
          <p>正在解析和验证数据，请稍候...</p>
        </div>

        <div v-else-if="validationResult" class="validation-result">
          <!-- 重要警告提示 -->
          <el-alert
            title="⚠️ 重要警告"
            type="error"
            :closable="false"
            show-icon
            class="upload-warning"
          >
            <template #default>
              <div class="warning-content">
                <p><strong>上传操作将会删除本地文件！</strong></p>
                <p>• 文件上传到云端后，本地原文件将被自动删除</p>
                <p>• 请确保您已备份重要文件</p>
                <p>• 此操作不可逆，请谨慎操作</p>
                <p>• 建议先在测试环境验证流程</p>
              </div>
            </template>
          </el-alert>

          <div class="validation-summary">
            <el-row :gutter="20">
              <el-col :span="6">
                <el-statistic title="总行数" :value="validationResult.totalRows" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="有效行数" :value="validationResult.validRows" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="错误行数" :value="validationResult.errorRows" />
              </el-col>
              <el-col :span="6">
                <el-statistic title="成功率" :value="successRate" suffix="%" />
              </el-col>
            </el-row>
          </div>

          <div v-if="validationResult.errors.length > 0" class="validation-errors">
            <h4>数据验证错误</h4>
            <el-alert
              v-for="(error, index) in validationResult.errors"
              :key="index"
              :title="error"
              type="error"
              :closable="false"
              class="error-item"
            />
          </div>

          <div v-if="validationResult.validData.length > 0" class="preview-data">
            <h4>预览有效数据（前5条）</h4>
            <el-table :data="previewData" border size="small">
              <el-table-column prop="title" label="标题" width="180" />
              <el-table-column prop="grade" label="年级" width="80" />
              <el-table-column prop="subject" label="科目" width="80" />
              <el-table-column prop="volume" label="册别" width="80" />
              <el-table-column prop="section" label="板块" width="100" />
              <el-table-column prop="features" label="特征" width="120">
                <template #default="{ row }">
                  <span>{{ Array.isArray(row.features) ? row.features.join(';') : row.features }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="file_path" label="文件路径" min-width="250" />
            </el-table>
          </div>
        </div>
      </div>

      <!-- 步骤3: 批量处理 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-header">
          <h3>批量处理</h3>
          <p>正在批量上传文件，请耐心等待...</p>
        </div>

        <div class="batch-progress">
          <el-progress
            :percentage="batchProgress"
            :status="batchStatus"
            :stroke-width="12"
          />
          <p class="progress-text">{{ progressText }}</p>

          <div v-if="batchResult" class="batch-summary">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-statistic title="总文件数" :value="batchResult.total" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="成功上传" :value="batchResult.success" />
              </el-col>
              <el-col :span="8">
                <el-statistic title="上传失败" :value="batchResult.failed" />
              </el-col>
            </el-row>
          </div>
        </div>
      </div>

      <!-- 步骤4: 完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="step-header">
          <h3>批量上传完成</h3>
        </div>

        <div class="completion-result">
          <el-result
            :icon="batchResult?.failed === 0 ? 'success' : 'warning'"
            :title="batchResult?.failed === 0 ? '全部上传成功' : '部分上传成功'"
          >
            <template #sub-title>
              <p>总共处理 {{ batchResult?.total }} 个文件</p>
              <p>成功上传 {{ batchResult?.success }} 个，失败 {{ batchResult?.failed }} 个</p>
            </template>
          </el-result>

          <div v-if="batchResult?.failed > 0" class="failed-files">
            <h4>上传失败的文件</h4>
            <el-table :data="failedFiles" border size="small">
              <el-table-column prop="row" label="行号" width="80" />
              <el-table-column prop="title" label="标题" width="200" />
              <el-table-column prop="filename" label="文件名" width="200" />
              <el-table-column prop="error" label="失败原因" min-width="300" />
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ currentStep === 3 ? '关闭' : '取消' }}
        </el-button>

        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="csvFileList.length === 0"
          @click="validateCsvData"
        >
          验证数据
        </el-button>

        <el-button
          v-if="currentStep === 1"
          type="primary"
          :disabled="!validationResult || validationResult.validRows === 0"
          @click="showUploadWarning"
        >
          开始上传
        </el-button>

        <el-button
          v-if="currentStep === 3"
          type="primary"
          @click="handleComplete"
        >
          完成
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { DocumentAdd, Download, Loading } from '@element-plus/icons-vue'
import { fileApi } from '@/api/files'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const csvUploadRef = ref()
const csvFileList = ref([])
const currentStep = ref(0)
const validating = ref(false)
const validationResult = ref(null)
const batchProgress = ref(0)
const batchStatus = ref('')
const progressText = ref('')
const batchResult = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const successRate = computed(() => {
  if (!validationResult.value) return 0
  const { validRows, totalRows } = validationResult.value
  return totalRows > 0 ? Math.round((validRows / totalRows) * 100) : 0
})

const previewData = computed(() => {
  if (!validationResult.value?.validData) return []
  return validationResult.value.validData.slice(0, 5)
})

const failedFiles = computed(() => {
  if (!batchResult.value?.results) return []
  return batchResult.value.results.filter(item => !item.success)
})

// 方法
const beforeCsvUpload = (file) => {
  const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv')
  if (!isCSV) {
    ElMessage.error('只支持CSV格式的文件')
    return false
  }

  const isValidSize = file.size / 1024 / 1024 < 10
  if (!isValidSize) {
    ElMessage.error('CSV文件大小不能超过10MB')
    return false
  }

  return false // 阻止自动上传
}

const handleCsvFileChange = (file, files) => {
  csvFileList.value = files
}

const handleCsvFileRemove = (file, files) => {
  csvFileList.value = files
}

const downloadTemplate = async () => {
  try {
    const result = await fileApi.downloadCsvTemplate()

    // 创建下载链接
    const blob = new Blob([result], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'files_upload_template.csv'
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('下载模板失败: ' + error.message)
  }
}

const validateCsvData = async () => {
  if (csvFileList.value.length === 0) {
    ElMessage.error('请选择CSV文件')
    return
  }

  validating.value = true
  currentStep.value = 1

  try {
    const csvFile = csvFileList.value[0].raw

    // 先进行数据验证（不实际上传文件）
    const formData = new FormData()
    formData.append('csvFile', csvFile)
    formData.append('validateOnly', 'true') // 标记仅验证

    const result = await fileApi.csvBatchUpload(formData)

    if (result.success) {
      // 设置验证结果
      validationResult.value = {
        totalRows: result.data.totalRows || 0,
        validRows: result.data.validRows || 0,
        errorRows: result.data.errorRows || 0,
        errors: result.data.errors || [],
        validData: result.data.validData || [],
        valid: result.data.valid
      }

      if (result.data.errors && result.data.errors.length > 0) {
        // 有验证错误
        ElMessage.warning(`数据验证完成，发现 ${result.data.errors.length} 个错误`)
      } else {
        // 验证通过
        ElMessage.success('数据验证通过，可以开始上传')
      }
    } else {
      validationResult.value = {
        totalRows: 0,
        validRows: 0,
        errorRows: 0,
        errors: [result.error || '验证失败'],
        validData: [],
        valid: false
      }
      ElMessage.error('数据验证失败: ' + (result.error || '未知错误'))
    }
  } catch (error) {
    ElMessage.error('验证失败: ' + error.message)
    currentStep.value = 0
  } finally {
    validating.value = false
  }
}

// 显示上传警告确认对话框
const showUploadWarning = () => {
  ElMessageBox.confirm(
    '上传操作将会删除本地文件！文件上传到云端后，本地原文件将被自动删除。此操作不可逆，请确保您已备份重要文件。是否继续？',
    '⚠️ 重要警告',
    {
      confirmButtonText: '我已备份，继续上传',
      cancelButtonText: '取消',
      type: 'error',
      dangerouslyUseHTMLString: false,
      showClose: false,
      closeOnClickModal: false,
      closeOnPressEscape: false,
      customClass: 'upload-warning-dialog'
    }
  ).then(() => {
    // 用户确认后执行上传
    startBatchUpload()
  }).catch(() => {
    ElMessage.info('已取消上传操作')
  })
}

const startBatchUpload = async () => {
  currentStep.value = 2
  batchProgress.value = 0
  batchStatus.value = ''
  progressText.value = '准备开始批量上传...'

  try {
    const csvFile = csvFileList.value[0].raw
    const formData = new FormData()
    formData.append('csvFile', csvFile)

    // 执行真实的批量上传
    const result = await fileApi.csvBatchUpload(formData, (progress) => {
      // 进度回调
      batchProgress.value = progress.percentage
      progressText.value = `正在处理第 ${progress.current} / ${progress.total} 个文件: ${progress.currentTitle || progress.currentFile}`
    })

    if (result.success) {
      batchStatus.value = result.data.failed === 0 ? 'success' : 'warning'
      batchProgress.value = 100
      progressText.value = '批量上传完成'
      batchResult.value = result.data

      ElMessage.success(`批量上传完成！成功: ${result.data.success}，失败: ${result.data.failed}`)
    } else {
      batchStatus.value = 'exception'
      progressText.value = '批量上传失败'
      ElMessage.error('批量上传失败: ' + result.error)
    }

    setTimeout(() => {
      currentStep.value = 3
    }, 1000)

  } catch (error) {
    batchStatus.value = 'exception'
    progressText.value = '批量上传异常'
    ElMessage.error('批量上传异常: ' + error.message)

    setTimeout(() => {
      currentStep.value = 3
    }, 1000)
  }
}

const handleComplete = () => {
  emit('success')
  handleClose()
}

const handleClose = () => {
  visible.value = false
  resetDialog()
}

const resetDialog = () => {
  currentStep.value = 0
  csvFileList.value = []
  csvUploadRef.value?.clearFiles()
  validating.value = false
  validationResult.value = null
  batchProgress.value = 0
  batchStatus.value = ''
  progressText.value = ''
  batchResult.value = null
}
</script>

<style scoped>
.csv-upload-content {
  padding: 20px 0;
}

.steps {
  margin-bottom: 40px;
}

.step-content {
  min-height: 400px;
}

.step-header {
  text-align: center;
  margin-bottom: 30px;
}

.step-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.step-header p {
  margin: 0;
  color: #909399;
}

/* 警告样式 */
.upload-warning {
  margin-bottom: 20px;
  border: 2px solid #f56c6c !important;
  background-color: #fef0f0 !important;
}

.upload-warning .warning-content {
  font-size: 14px;
  line-height: 1.6;
}

.upload-warning .warning-content p {
  margin: 8px 0;
}

.upload-warning .warning-content p:first-child {
  font-size: 16px;
  color: #f56c6c;
  font-weight: bold;
}

.upload-warning .warning-content p:not(:first-child) {
  color: #e6a23c;
  margin-left: 16px;
}

/* 警告对话框样式 */
:deep(.upload-warning-dialog) {
  .el-message-box__header {
    background-color: #fef0f0;
    border-bottom: 2px solid #f56c6c;
  }

  .el-message-box__title {
    color: #f56c6c;
    font-weight: bold;
    font-size: 18px;
  }

  .el-message-box__content {
    padding: 20px;
    font-size: 16px;
    line-height: 1.6;
    color: #e6a23c;
  }

  .el-button--primary {
    background-color: #f56c6c;
    border-color: #f56c6c;
    font-weight: bold;
  }

  .el-button--primary:hover {
    background-color: #f78989;
    border-color: #f78989;
  }
}

.csv-upload-area {
  margin-bottom: 20px;
}

.upload-content {
  padding: 40px 20px;
  text-align: center;
}

.upload-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 16px;
}

.upload-text p {
  margin: 8px 0;
  color: #606266;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
}

.template-section {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 4px;
}

.template-actions {
  margin-top: 16px;
  text-align: center;
}

.validation-loading {
  text-align: center;
  padding: 60px 0;
}

.validation-loading p {
  margin-top: 16px;
  color: #606266;
}

.validation-summary {
  margin-bottom: 30px;
}

.validation-errors {
  margin-bottom: 30px;
}

.validation-errors h4 {
  margin: 0 0 16px 0;
  color: #f56c6c;
}

.error-item {
  margin-bottom: 8px;
}

.preview-data h4 {
  margin: 0 0 16px 0;
  color: #303133;
}

.batch-progress {
  text-align: center;
  padding: 40px 0;
}

.progress-text {
  margin: 16px 0;
  color: #606266;
}

.batch-summary {
  margin-top: 30px;
}

.completion-result {
  text-align: center;
}

.failed-files {
  margin-top: 30px;
  text-align: left;
}

.failed-files h4 {
  margin: 0 0 16px 0;
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
