// 预览图生成服务
const fs = require('fs')
const path = require('path')
const sharp = require('sharp')
const { app: cloudbase } = require('../config/cloudbase')
const { v4: uuidv4 } = require('uuid')

class PreviewService {
  constructor() {
    this.app = cloudbase

    // 预览图配置
    this.previewConfig = {
      width: 400,
      height: 600,
      quality: 80,
      format: 'jpeg',
      maxPages: 3
    }
  }

  // 生成PDF预览图（需要安装poppler-utils）
  async generatePdfPreviews(pdfPath, dateFolder = null, fileName = null) {
    try {
      console.log('开始生成PDF预览图:', pdfPath)

      // 检查是否安装了pdf-poppler
      let pdfPoppler
      try {
        pdfPoppler = require('pdf-poppler')
        console.log('pdf-poppler库加载成功')
      } catch (error) {
        console.warn('pdf-poppler未安装，跳过PDF预览图生成')
        return {
          success: true,
          data: [],
          message: 'PDF预览图生成功能需要安装poppler-utils'
        }
      }

      const outputDir = path.join(__dirname, '../../uploads/temp/previews')
      console.log('预览图输出目录:', outputDir)

      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
        console.log('创建预览图目录成功')
      }

      const options = {
        format: 'jpeg',
        out_dir: outputDir,
        out_prefix: `preview_${uuidv4()}`,
        page: null, // 生成所有页面
        quality: this.previewConfig.quality
      }

      console.log('PDF转换选项:', options)

      // 生成预览图
      console.log('开始PDF转换...')
      const convertResult = await pdfPoppler.convert(pdfPath, options)
      console.log('PDF转换完成，结果:', convertResult)

      // 获取PDF页数信息来确定生成的页面数
      const pdfInfo = await pdfPoppler.info(pdfPath)
      const totalPages = parseInt(pdfInfo.pages) || 0
      console.log('PDF总页数:', totalPages)

      const previewImages = []
      const maxPages = Math.min(totalPages, this.previewConfig.maxPages)
      console.log(`处理预览图，总页数: ${totalPages}, 最大处理页数: ${maxPages}`)

      for (let i = 0; i < maxPages; i++) {
        const imagePath = path.join(outputDir, `${options.out_prefix}-${i + 1}.jpg`)

        if (fs.existsSync(imagePath)) {
          // 压缩和调整尺寸
          const compressedPath = await this.compressImage(imagePath)

          // 上传到云存储（使用新的命名规则）
          const uploadResult = await this.uploadPreviewImage(compressedPath, i + 1, dateFolder, fileName)

          if (uploadResult.success) {
            previewImages.push({
              url: uploadResult.data.fileID,
              order: i + 1,
              width: this.previewConfig.width,
              height: this.previewConfig.height
            })
          }

          // 清理临时文件
          try {
            fs.unlinkSync(imagePath)
            if (compressedPath !== imagePath) {
              fs.unlinkSync(compressedPath)
            }
          } catch (cleanupError) {
            // 静默处理清理错误
          }
        }
      }

      return {
        success: true,
        data: previewImages
      }
    } catch (error) {
      console.error('生成PDF预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 压缩图片
  async compressImage(imagePath) {
    try {
      const outputPath = imagePath.replace('.jpg', '_compressed.jpg')

      await sharp(imagePath)
        .resize(this.previewConfig.width, this.previewConfig.height, {
          fit: 'inside',
          withoutEnlargement: true
        })
        .jpeg({
          quality: this.previewConfig.quality,
          progressive: true
        })
        .toFile(outputPath)

      return outputPath
    } catch (error) {
      console.error('压缩图片失败:', error)
      return imagePath // 返回原图片路径
    }
  }

  // 上传预览图到云存储
  async uploadPreviewImage(imagePath, pageNumber, dateFolder = null, fileName = null) {
    try {
      let previewFileName, cloudPath

      console.log('uploadPreviewImage参数检查:', {
        dateFolder: dateFolder,
        fileName: fileName,
        dateFolderType: typeof dateFolder,
        fileNameType: typeof fileName,
        dateFolderTruthy: !!dateFolder,
        fileNameTruthy: !!fileName
      })

      if (dateFolder && fileName) {
        // 使用新的命名规则：原文件名_页码.jpg（保持原文件名不变）
        console.log('使用新的命名规则')
        const ext = path.extname(fileName)
        const nameWithoutExt = path.basename(fileName, ext)
        previewFileName = `${nameWithoutExt}_${pageNumber}.jpg`
        cloudPath = `files/${dateFolder}/${previewFileName}`

        console.log('预览图文件名信息:', {
          原始文件名: fileName,
          去扩展名: nameWithoutExt,
          预览图文件名: previewFileName,
          云存储路径: cloudPath
        })
      } else {
        // 兼容旧的命名规则
        console.log('使用旧的命名规则，原因:', { dateFolder, fileName })
        previewFileName = `preview_${uuidv4()}_page${pageNumber}.jpg`
        cloudPath = `files/previews/${previewFileName}`
      }

      console.log('预览图上传信息:', {
        pageNumber,
        dateFolder,
        originalFileName: fileName,
        previewFileName,
        cloudPath
      })

      const fileStream = fs.createReadStream(imagePath)

      const result = await this.app.uploadFile({
        cloudPath,
        fileContent: fileStream
      })

      return {
        success: true,
        data: {
          fileID: result.fileID,
          cloudPath
        }
      }
    } catch (error) {
      console.error('上传预览图失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 生成Word文档预览图（简化版本）
  async generateWordPreviews(wordPath, dateFolder = null, fileName = null) {
    try {
      // Word文档预览图生成比较复杂，需要LibreOffice或其他工具
      // 这里返回空数组，实际部署时可以考虑使用LibreOffice headless模式
      console.log('Word文档预览图生成功能待实现')

      return {
        success: true,
        data: [],
        message: 'Word文档预览图生成功能待实现'
      }
    } catch (error) {
      console.error('生成Word预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 生成PowerPoint预览图（简化版本）
  async generatePptPreviews(pptPath, dateFolder = null, fileName = null) {
    try {
      // PowerPoint预览图生成需要LibreOffice或其他工具
      console.log('PowerPoint预览图生成功能待实现')

      return {
        success: true,
        data: [],
        message: 'PowerPoint预览图生成功能待实现'
      }
    } catch (error) {
      console.error('生成PowerPoint预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 根据文件类型生成预览图
  async generatePreviews(filePath, fileType, dateFolder = null, fileName = null) {
    try {
      switch (fileType.toLowerCase()) {
        case 'pdf':
          return await this.generatePdfPreviews(filePath, dateFolder, fileName)

        case 'doc':
        case 'docx':
          return await this.generateWordPreviews(filePath, dateFolder, fileName)

        case 'ppt':
        case 'pptx':
          return await this.generatePptPreviews(filePath, dateFolder, fileName)

        default:
          return {
            success: true,
            data: [],
            message: `不支持 ${fileType} 格式的预览图生成`
          }
      }
    } catch (error) {
      console.error('生成预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 删除预览图
  async deletePreviews(previewImages) {
    try {
      if (!previewImages || previewImages.length === 0) {
        return { success: true }
      }

      const fileIds = previewImages.map(img => img.url)

      const result = await this.app.deleteFile({
        fileList: fileIds
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('删除预览图失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取预览图下载链接
  async getPreviewUrls(previewImages) {
    try {
      if (!previewImages || previewImages.length === 0) {
        return {
          success: true,
          data: []
        }
      }

      const fileList = previewImages.map(img => ({
        fileID: img.url,
        maxAge: 3600 // 1小时有效期
      }))

      const result = await this.app.getTempFileURL({ fileList })

      const previewUrls = []
      if (result.fileList) {
        result.fileList.forEach((file, index) => {
          if (file.code === 'SUCCESS') {
            previewUrls.push({
              ...previewImages[index],
              tempUrl: file.tempFileURL
            })
          }
        })
      }

      return {
        success: true,
        data: previewUrls
      }
    } catch (error) {
      console.error('获取预览图链接失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 检查系统依赖
  checkDependencies() {
    const dependencies = {
      'pdf-poppler': false,
      'sharp': false
    }

    try {
      require('pdf-poppler')
      dependencies['pdf-poppler'] = true
    } catch (error) {
      console.warn('pdf-poppler未安装，PDF预览图功能不可用')
    }

    try {
      require('sharp')
      dependencies['sharp'] = true
    } catch (error) {
      console.warn('sharp未安装，图片处理功能不可用')
    }

    return dependencies
  }
}

module.exports = new PreviewService()
