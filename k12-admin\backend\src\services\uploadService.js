// 文件上传服务
const { app: cloudbase, db } = require('../config/cloudbase')
const fs = require('fs')
const path = require('path')
const sharp = require('sharp')
const { v4: uuidv4 } = require('uuid')
const previewService = require('./previewService')

class UploadService {
  constructor() {
    this.db = db
    this.app = cloudbase
    this.filesCollection = this.db.collection('files')

    // 支持的文件类型
    this.allowedTypes = {
      'application/pdf': 'pdf',
      'application/msword': 'doc',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
      'application/vnd.ms-powerpoint': 'ppt',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
      'application/vnd.ms-excel': 'xls',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx'
    }

    // 最大文件大小 (50MB)
    this.maxFileSize = 50 * 1024 * 1024
  }

  // 生成当日日期文件夹名称
  generateDateFolder() {
    const now = new Date()
    const year = now.getFullYear()
    const month = String(now.getMonth() + 1).padStart(2, '0')
    const day = String(now.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 生成规范化的文件名（保持原文件名不变）
  generateFileName(originalName, timestamp = null) {
    if (!timestamp) {
      timestamp = Date.now()
    }

    // 直接返回原文件名，不做任何修改
    return originalName
  }

  // 生成预览图文件名（保持原文件名不变）
  generatePreviewFileName(baseFileName, pageNumber) {
    const ext = path.extname(baseFileName)
    const nameWithoutExt = path.basename(baseFileName, ext)
    return `${nameWithoutExt}_${pageNumber}.jpg`
  }

  // 验证文件
  validateFile(file) {
    // 检查文件类型
    if (!this.allowedTypes[file.mimetype]) {
      return {
        valid: false,
        error: `不支持的文件类型: ${file.mimetype}`
      }
    }

    // 检查文件大小
    if (file.size > this.maxFileSize) {
      return {
        valid: false,
        error: `文件大小超过限制 (最大50MB)`
      }
    }

    return { valid: true }
  }

  // 上传单个文件到云存储
  async uploadToCloudStorage(file, cloudPath) {
    try {
      const fileStream = fs.createReadStream(file.path)

      const result = await this.app.uploadFile({
        cloudPath,
        fileContent: fileStream
      })

      return {
        success: true,
        data: {
          fileID: result.fileID,
          cloudPath
        }
      }
    } catch (error) {
      console.error('上传到云存储失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取PDF页数
  async getPdfPageCount(filePath) {
    try {
      const pdfPoppler = require('pdf-poppler')
      const pdfInfo = await pdfPoppler.info(filePath)
      console.log('PDF信息:', pdfInfo)
      return parseInt(pdfInfo.pages) || null
    } catch (error) {
      console.warn('获取PDF页数失败:', error)
      return null
    }
  }

  // 生成预览图
  async generatePreviewImages(file, dateFolder, fileName) {
    try {
      const fileExtension = this.allowedTypes[file.mimetype]

      // 使用预览图生成服务（传递文件夹和文件名信息）
      const result = await previewService.generatePreviews(file.path, fileExtension, dateFolder, fileName)

      return result
    } catch (error) {
      console.error('生成预览图失败:', error)
      return {
        success: false,
        error: error.message,
        data: []
      }
    }
  }

  // 上传文件并保存到数据库
  async uploadFile(file, metadata) {
    try {
      // 验证文件
      const validation = this.validateFile(file)
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error
        }
      }

      // 生成文件管理路径
      const timestamp = Date.now()
      const dateFolder = this.generateDateFolder()
      const fileName = this.generateFileName(file.originalname, timestamp)
      const cloudPath = `files/${dateFolder}/${fileName}`

      console.log('文件上传信息:', {
        originalName: file.originalname,
        originalNameBuffer: Buffer.from(file.originalname),
        dateFolder,
        fileName,
        fileNameBuffer: Buffer.from(fileName),
        cloudPath,
        cloudPathBuffer: Buffer.from(cloudPath)
      })

      // 上传到云存储
      const uploadResult = await this.uploadToCloudStorage(file, cloudPath)
      if (!uploadResult.success) {
        return uploadResult
      }

      // 生成预览图（使用新的命名规则）
      let previewResult = { success: true, data: [] }
      try {
        console.log('开始生成预览图，参数:', { dateFolder, fileName })
        previewResult = await this.generatePreviewImages(file, dateFolder, fileName)
        console.log('预览图生成结果:', previewResult)
      } catch (previewError) {
        console.warn('预览图生成失败，但不影响文件上传:', previewError.message)
        previewResult = { success: true, data: [] }
      }

      // 获取页数（仅PDF文件）
      let pages = null
      const fileExtension = this.allowedTypes[file.mimetype]
      if (fileExtension === 'pdf') {
        pages = await this.getPdfPageCount(file.path)
        console.log(`PDF文件页数: ${pages}`)
      }

      // 准备数据库记录
      const fileRecord = {
        title: metadata.title || file.originalname,
        description: metadata.description || '',
        file_url: uploadResult.data.fileID,
        file_path: cloudPath, // 新的云存储路径
        original_name: file.originalname, // 原始文件名
        file_type: fileExtension,
        file_size: file.size,
        preview_images: previewResult.data || [],
        category: metadata.category || 'regular',
        grade: metadata.grade || null,
        subject: metadata.subject || '',
        volume: metadata.volume || '',
        section: metadata.section || '',
        tags: metadata.tags || [],
        ad_required_count: parseInt(metadata.ad_required_count) || 1,
        uploader_id: 'admin',
        created_time: new Date(),
        upload_date: dateFolder, // 上传日期文件夹
        status: 'active',
        sort_order: parseInt(metadata.sort_order) || 0,
        pages: pages, // PDF页数
        features: metadata.features || [],
        download_count: parseInt(metadata.download_count) || 0,
        view_count: parseInt(metadata.view_count) || 0
      }

      console.log('uploadService接收到的metadata:', {
        download_count: metadata.download_count,
        view_count: metadata.view_count,
        download_count_type: typeof metadata.download_count,
        view_count_type: typeof metadata.view_count
      })

      console.log('数据库记录:', {
        title: fileRecord.title,
        file_path: fileRecord.file_path,
        original_name: fileRecord.original_name,
        download_count: fileRecord.download_count,
        view_count: fileRecord.view_count,
        upload_date: fileRecord.upload_date,
        preview_images_count: fileRecord.preview_images.length
      })

      // 保存到数据库
      console.log('开始保存到数据库...')
      console.log('数据库集合:', this.filesCollection)
      console.log('要保存的记录:', JSON.stringify(fileRecord, null, 2))

      const dbResult = await this.filesCollection.add(fileRecord)
      console.log('数据库保存成功:', {
        id: dbResult.id,
        title: fileRecord.title,
        file_path: fileRecord.file_path
      })

      // 清理临时文件
      try {
        fs.unlinkSync(file.path)
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError)
      }

      return {
        success: true,
        data: {
          _id: dbResult.id,
          ...fileRecord
        }
      }
    } catch (error) {
      console.error('上传文件失败:', {
        message: error.message,
        stack: error.stack,
        fileName: file?.originalname,
        fileSize: file?.size
      })

      // 清理临时文件
      try {
        if (file.path) {
          fs.unlinkSync(file.path)
        }
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError)
      }

      return {
        success: false,
        error: error.message
      }
    }
  }

  // 批量上传文件
  async batchUploadFiles(files, commonMetadata) {
    try {
      const results = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        const metadata = {
          ...commonMetadata,
          title: commonMetadata.title || file.originalname
        }

        const result = await this.uploadFile(file, metadata)
        results.push({
          filename: file.originalname,
          success: result.success,
          data: result.data,
          error: result.error
        })
      }

      const successCount = results.filter(r => r.success).length
      const failCount = results.filter(r => !r.success).length

      return {
        success: true,
        data: {
          total: files.length,
          success: successCount,
          failed: failCount,
          results
        }
      }
    } catch (error) {
      console.error('批量上传失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 获取文件下载URL
  async getDownloadUrl(fileId) {
    try {
      const fileResult = await this.filesCollection.doc(fileId).get()

      if (!fileResult.data.length) {
        return {
          success: false,
          error: '文件不存在'
        }
      }

      const fileData = fileResult.data[0]

      // 验证文件URL格式
      if (!fileData.file_url) {
        return {
          success: false,
          error: '文件URL为空'
        }
      }

      // 获取临时下载链接
      console.log('正在获取文件下载链接:', {
        fileId: fileId,
        fileUrl: fileData.file_url,
        title: fileData.title,
        urlFormat: fileData.file_url.startsWith('cloud://') ? 'cloud://' : 'other'
      })

      // 获取临时下载链接
      console.log('调用getTempFileURL:', {
        fileID: fileData.file_url,
        maxAge: 3600
      })

      const urlResult = await this.app.getTempFileURL({
        fileList: [{
          fileID: fileData.file_url,
          maxAge: 3600 // 1小时有效期
        }]
      })

      console.log('云存储返回结果:', {
        success: !!urlResult.fileList,
        count: urlResult.fileList?.length || 0,
        firstCode: urlResult.fileList?.[0]?.code,
        result: urlResult
      })

      if (urlResult.fileList && urlResult.fileList.length > 0) {
        const fileInfo = urlResult.fileList[0]
        console.log('文件信息:', fileInfo)

        if (fileInfo.code === 'SUCCESS') {
          return {
            success: true,
            data: {
              downloadUrl: fileInfo.tempFileURL,
              filename: fileData.title,
              fileType: fileData.file_type,
              fileSize: fileData.file_size
            }
          }
        } else if (fileInfo.code === 'STORAGE_FILE_NONEXIST') {
          console.error('云存储中文件不存在:', fileData.file_url)
          console.error('这是测试数据问题，所有文件都指向同一个不存在的文件')
          console.error('建议：1. 上传真实文件 2. 更新数据库中的file_url字段')
          return {
            success: false,
            error: '文件在云存储中不存在。这是测试数据问题，所有文件都指向同一个不存在的文件。请上传真实文件或联系管理员修复数据。'
          }
        } else {
          console.error('获取临时URL失败:', fileInfo)
          return {
            success: false,
            error: `获取下载链接失败: ${fileInfo.code} - ${fileInfo.message || '未知错误'}`
          }
        }
      }

      return {
        success: false,
        error: '云存储未返回有效的文件信息'
      }
    } catch (error) {
      console.error('获取下载链接失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 更新文件下载统计
  async updateDownloadStats(fileId) {
    try {
      await this.filesCollection.doc(fileId).update({
        download_count: this.db.command.inc(1)
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('更新下载统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 更新文件查看统计
  async updateViewStats(fileId) {
    try {
      await this.filesCollection.doc(fileId).update({
        view_count: this.db.command.inc(1)
      })

      return {
        success: true
      }
    } catch (error) {
      console.error('更新查看统计失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  // 注意：腾讯云开发的云存储文件默认是私有权限
  // 不能直接通过HTTPS链接访问，必须使用getTempFileURL获取临时链接
}

module.exports = new UploadService()
